import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  cnic?: string;
  user_type: 'admin' | 'player' | 'venue_owner';
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  strikes: number;
  profile_image_url?: string;
  address?: string;
  city?: string;
  created_at: string;
  updated_at: string;
}

export interface Venue {
  id: string;
  owner_id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  opening_time: string;
  closing_time: string;
  day_price_per_hour: number;
  night_price_per_hour: number;
  weekend_day_price?: number;
  weekend_night_price?: number;
  night_time_start: string;
  facilities?: string[];
  rules?: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  strikes: number;
  rating: number;
  total_reviews: number;
  created_at: string;
  updated_at: string;
  approved_by?: string;
  approved_at?: string;
}

export interface Booking {
  id: string;
  player_id: string;
  venue_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_hours: number;
  price_per_hour: number;
  total_amount: number;
  is_weekend: boolean;
  is_night_time: boolean;
  status: 'pending' | 'confirmed' | 'rejected' | 'cancelled' | 'completed';
  player_notes?: string;
  venue_response?: string;
  payment_status: 'pending' | 'paid' | 'refunded';
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
}

export interface Review {
  id: string;
  booking_id: string;
  reviewer_id: string;
  reviewee_id: string;
  venue_id?: string;
  rating: number;
  comment?: string;
  review_type: 'player_to_venue' | 'venue_to_player';
  is_anonymous: boolean;
  created_at: string;
  updated_at: string;
}

export interface Strike {
  id: string;
  user_id?: string;
  venue_id?: string;
  booking_id?: string;
  strike_type: string;
  reason: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  issued_by: string;
  status: 'active' | 'resolved' | 'appealed';
  created_at: string;
  resolved_at?: string;
  resolution_notes?: string;
}

export interface VenueImage {
  id: string;
  venue_id: string;
  image_url: string;
  is_primary: boolean;
  display_order: number;
  created_at: string;
}

export interface TimeSlot {
  id: string;
  venue_id: string;
  slot_date: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  is_blocked: boolean;
  blocked_reason?: string;
  price: number;
  is_weekend: boolean;
  is_night_time: boolean;
  created_at: string;
}

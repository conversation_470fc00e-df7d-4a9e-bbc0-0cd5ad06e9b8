import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthContextType } from '@/types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock users database
  const [users] = useState<(User & { password: string })[]>([
    {
      id: 'admin-1',
      email: '<EMAIL>',
      password: 'admin123',
      name: 'System Administrator',
      user_type: 'admin',
      created_at: new Date('2024-01-01'),
      updated_at: new Date('2024-01-01'),
    },
    {
      id: 'player-1',
      email: '<EMAIL>',
      password: 'player123',
      name: '<PERSON>',
      user_type: 'player',
      phone: '+**********',
      created_at: new Date('2024-01-15'),
      updated_at: new Date('2024-01-15'),
    },
    {
      id: 'owner-1',
      email: '<EMAIL>',
      password: 'owner123',
      name: 'Jane Owner',
      user_type: 'venue_owner',
      phone: '+**********',
      created_at: new Date('2024-01-20'),
      updated_at: new Date('2024-01-20'),
    },
  ]);

  useEffect(() => {
    // Simulate checking stored auth state
    const checkAuthState = async () => {
      try {
        // In a real app, check AsyncStorage for stored token
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLoading(false);
      } catch (error) {
        console.error('Auth check failed:', error);
        setLoading(false);
      }
    };
    
    checkAuthState();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const foundUser = users.find(u => 
        u.email.toLowerCase() === email.toLowerCase() && 
        u.password === password
      );
      
      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      const { password: _, ...userWithoutPassword } = foundUser;
      setUser(userWithoutPassword);
      
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>, password: string) => {
    try {
      // Check if user already exists
      if (users.find(u => u.email.toLowerCase() === userData.email.toLowerCase())) {
        throw new Error('An account with this email already exists');
      }

      // In a real app, this would create a new user in the database
      throw new Error('Sign up functionality would be implemented with a real backend');
      
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      setUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Venue, Booking, Review } from '@/types';

interface DataContextType {
  venues: Venue[];
  bookings: Booking[];
  reviews: Review[];
  addVenue: (venue: Omit<Venue, 'id' | 'created_at' | 'updated_at' | 'total_bookings' | 'rating'>) => void;
  updateVenue: (id: string, updates: Partial<Venue>) => void;
  addBooking: (booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => void;
  updateBooking: (id: string, updates: Partial<Booking>) => void;
  addReview: (review: Omit<Review, 'id' | 'created_at'>) => void;
  approveVenue: (id: string) => void;
  rejectVenue: (id: string) => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function DataProvider({ children }: { children: ReactNode }) {
  const [venues, setVenues] = useState<Venue[]>([
    {
      id: '1',
      owner_id: 'owner-1',
      name: 'Elite Football Arena',
      description: 'Professional 11v11 football pitch with FIFA standard grass and floodlights. Perfect for competitive matches and training sessions.',
      address: '123 Sports Complex, Downtown District',
      city: 'New York',
      price_per_hour: 80,
      images: [
        'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg',
        'https://images.pexels.com/photos/1618269/pexels-photo-1618269.jpeg',
        'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg'
      ],
      amenities: ['Floodlights', 'Changing Rooms', 'Parking', 'Refreshments', 'First Aid'],
      available_hours: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'],
      capacity: 22,
      status: 'approved',
      rating: 4.8,
      total_bookings: 156,
      latitude: 40.7128,
      longitude: -74.0060,
      created_at: new Date('2024-01-15'),
      updated_at: new Date('2024-01-15'),
    },
    {
      id: '2',
      owner_id: 'owner-1',
      name: 'Community Sports Ground',
      description: '7v7 artificial turf pitch perfect for small-sided games and casual matches. Great for friends and local teams.',
      address: '456 Community Center, Uptown',
      city: 'New York',
      price_per_hour: 45,
      images: [
        'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg',
        'https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg'
      ],
      amenities: ['Artificial Turf', 'Goals', 'Parking', 'Water Fountain'],
      available_hours: ['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00'],
      capacity: 14,
      status: 'approved',
      rating: 4.2,
      total_bookings: 89,
      latitude: 40.7589,
      longitude: -73.9851,
      created_at: new Date('2024-02-01'),
      updated_at: new Date('2024-02-01'),
    },
    {
      id: '3',
      owner_id: 'owner-1',
      name: 'Metro Football Complex',
      description: 'Modern indoor football facility with climate control. Available year-round regardless of weather conditions.',
      address: '789 Metro Plaza, Central City',
      city: 'New York',
      price_per_hour: 65,
      images: [
        'https://images.pexels.com/photos/1618269/pexels-photo-1618269.jpeg',
        'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg'
      ],
      amenities: ['Indoor Facility', 'Climate Control', 'Changing Rooms', 'Parking', 'Equipment Rental'],
      available_hours: ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'],
      capacity: 18,
      status: 'pending',
      rating: 0,
      total_bookings: 0,
      latitude: 40.7282,
      longitude: -73.9942,
      created_at: new Date('2024-03-01'),
      updated_at: new Date('2024-03-01'),
    },
  ]);

  const [bookings, setBookings] = useState<Booking[]>([
    {
      id: '1',
      player_id: 'player-1',
      venue_id: '1',
      date: '2024-03-15',
      start_time: '14:00',
      end_time: '16:00',
      total_hours: 2,
      total_amount: 160,
      status: 'confirmed',
      payment_status: 'paid',
      notes: 'Team practice session',
      created_at: new Date('2024-03-10'),
      updated_at: new Date('2024-03-10'),
    },
    {
      id: '2',
      player_id: 'player-1',
      venue_id: '2',
      date: '2024-03-20',
      start_time: '18:00',
      end_time: '19:00',
      total_hours: 1,
      total_amount: 45,
      status: 'pending',
      payment_status: 'pending',
      notes: 'Casual game with friends',
      created_at: new Date('2024-03-12'),
      updated_at: new Date('2024-03-12'),
    },
  ]);

  const [reviews, setReviews] = useState<Review[]>([
    {
      id: '1',
      venue_id: '1',
      player_id: 'player-1',
      rating: 5,
      comment: 'Excellent facility with great lighting and well-maintained pitch. Highly recommended!',
      created_at: new Date('2024-03-11'),
    },
    {
      id: '2',
      venue_id: '2',
      player_id: 'player-1',
      rating: 4,
      comment: 'Good venue for casual games. Could use better parking facilities.',
      created_at: new Date('2024-02-15'),
    },
  ]);

  const addVenue = (venueData: Omit<Venue, 'id' | 'created_at' | 'updated_at' | 'total_bookings' | 'rating'>) => {
    const newVenue: Venue = {
      ...venueData,
      id: Date.now().toString(),
      total_bookings: 0,
      rating: 0,
      created_at: new Date(),
      updated_at: new Date(),
    };
    setVenues(prev => [...prev, newVenue]);
  };

  const updateVenue = (id: string, updates: Partial<Venue>) => {
    setVenues(prev => prev.map(venue => 
      venue.id === id ? { ...venue, ...updates, updated_at: new Date() } : venue
    ));
  };

  const addBooking = (bookingData: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => {
    const newBooking: Booking = {
      ...bookingData,
      id: Date.now().toString(),
      created_at: new Date(),
      updated_at: new Date(),
    };
    setBookings(prev => [...prev, newBooking]);
    
    // Update venue total bookings
    setVenues(prev => prev.map(venue => 
      venue.id === bookingData.venue_id 
        ? { ...venue, total_bookings: venue.total_bookings + 1 }
        : venue
    ));
  };

  const updateBooking = (id: string, updates: Partial<Booking>) => {
    setBookings(prev => prev.map(booking => 
      booking.id === id ? { ...booking, ...updates, updated_at: new Date() } : booking
    ));
  };

  const addReview = (reviewData: Omit<Review, 'id' | 'created_at'>) => {
    const newReview: Review = {
      ...reviewData,
      id: Date.now().toString(),
      created_at: new Date(),
    };
    setReviews(prev => [...prev, newReview]);
    
    // Update venue rating
    const venueReviews = [...reviews, newReview].filter(r => r.venue_id === reviewData.venue_id);
    const averageRating = venueReviews.reduce((sum, r) => sum + r.rating, 0) / venueReviews.length;
    
    setVenues(prev => prev.map(venue => 
      venue.id === reviewData.venue_id 
        ? { ...venue, rating: Math.round(averageRating * 10) / 10 }
        : venue
    ));
  };

  const approveVenue = (id: string) => {
    updateVenue(id, { status: 'approved' });
  };

  const rejectVenue = (id: string) => {
    updateVenue(id, { status: 'rejected' });
  };

  return (
    <DataContext.Provider value={{
      venues,
      bookings,
      reviews,
      addVenue,
      updateVenue,
      addBooking,
      updateBooking,
      addReview,
      approveVenue,
      rejectVenue,
    }}>
      {children}
    </DataContext.Provider>
  );
}

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}
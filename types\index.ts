export interface User {
  id: string;
  email: string;
  name: string;
  user_type: 'player' | 'venue_owner' | 'admin';
  phone?: string;
  avatar?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Venue {
  id: string;
  owner_id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  price_per_hour: number;
  images: string[];
  amenities: string[];
  available_hours: string[];
  capacity: number;
  status: 'pending' | 'approved' | 'rejected';
  rating: number;
  total_bookings: number;
  latitude?: number;
  longitude?: number;
  created_at: Date;
  updated_at: Date;
}

export interface Booking {
  id: string;
  player_id: string;
  venue_id: string;
  date: string;
  start_time: string;
  end_time: string;
  total_hours: number;
  total_amount: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  payment_status: 'pending' | 'paid' | 'refunded';
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Review {
  id: string;
  venue_id: string;
  player_id: string;
  rating: number;
  comment: string;
  created_at: Date;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}
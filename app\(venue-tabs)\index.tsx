import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { DollarSign, Calendar, MapPin, Star, TrendingUp, Users, Plus } from 'lucide-react-native';

export default function VenueDashboard() {
  const { user } = useAuth();
  const { venues, bookings } = useData();

  const ownerVenues = venues.filter(venue => venue.owner_id === user?.id);
  const venueBookings = bookings.filter(booking => 
    ownerVenues.some(venue => venue.id === booking.venue_id)
  );

  const stats = [
    {
      label: 'Total Revenue',
      value: `$${venueBookings.reduce((sum, booking) => sum + booking.total_amount, 0).toLocaleString()}`,
      icon: DollarSign,
      color: '#22C55E',
      bgColor: '#F0FDF4',
    },
    {
      label: 'Total Bookings',
      value: venueBookings.length.toString(),
      icon: Calendar,
      color: '#3B82F6',
      bgColor: '#EFF6FF',
    },
    {
      label: 'Active Venues',
      value: ownerVenues.filter(v => v.status === 'approved').length.toString(),
      icon: MapPin,
      color: '#F59E0B',
      bgColor: '#FEF3C7',
    },
    {
      label: 'Avg Rating',
      value: ownerVenues.length > 0 ? 
        (ownerVenues.reduce((sum, venue) => sum + venue.rating, 0) / ownerVenues.length).toFixed(1) : 
        '0.0',
      icon: Star,
      color: '#EF4444',
      bgColor: '#FEF2F2',
    },
  ];

  const recentBookings = venueBookings.slice(0, 5);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Welcome back, {user?.name?.split(' ')[0]}!</Text>
          <Text style={styles.subtitle}>Here's how your venues are performing</Text>
        </View>

        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { backgroundColor: stat.bgColor }]}>
              <stat.icon size={24} color={stat.color} />
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Bookings</Text>
            <TouchableOpacity>
              <Text style={styles.sectionAction}>View All</Text>
            </TouchableOpacity>
          </View>

          {recentBookings.length > 0 ? (
            <View style={styles.bookingsList}>
              {recentBookings.map((booking) => {
                const venue = ownerVenues.find(v => v.id === booking.venue_id);
                return (
                  <View key={booking.id} style={styles.bookingCard}>
                    <View style={styles.bookingInfo}>
                      <Text style={styles.bookingVenue}>{venue?.name}</Text>
                      <Text style={styles.bookingDate}>
                        {new Date(booking.date).toLocaleDateString()} • {booking.start_time}
                      </Text>
                    </View>
                    <View style={styles.bookingMeta}>
                      <Text style={styles.bookingPrice}>${booking.total_amount}</Text>
                      <View style={[styles.statusBadge, { 
                        backgroundColor: booking.status === 'confirmed' ? '#22C55E20' : '#F59E0B20' 
                      }]}>
                        <Text style={[styles.statusText, { 
                          color: booking.status === 'confirmed' ? '#22C55E' : '#F59E0B' 
                        }]}>
                          {booking.status}
                        </Text>
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
          ) : (
            <View style={styles.emptyBookings}>
              <Calendar size={32} color="#D1D5DB" />
              <Text style={styles.emptyText}>No bookings yet</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionCard}>
              <Plus size={24} color="#3B82F6" />
              <Text style={styles.actionText}>Add New Venue</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <TrendingUp size={24} color="#22C55E" />
              <Text style={styles.actionText}>View Analytics</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    marginTop: -12,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    width: '47%',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  sectionAction: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  bookingsList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  bookingInfo: {
    flex: 1,
  },
  bookingVenue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  bookingDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookingMeta: {
    alignItems: 'flex-end',
  },
  bookingPrice: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#22C55E',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textTransform: 'capitalize',
  },
  emptyBookings: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 8,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  actionCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginTop: 8,
    textAlign: 'center',
  },
});